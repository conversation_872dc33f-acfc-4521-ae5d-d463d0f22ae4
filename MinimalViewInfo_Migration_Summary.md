# MinimalViewInfo 迁移总结

## 概述
成功将现有的基于Matrix的世界坐标转屏幕坐标系统改为使用MinimalViewInfo结构体的方式。这个改动使代码更加直观和易于理解。

## 主要更改

### 1. 新增结构体定义 (ImGuiELGS.h)
```cpp
struct FRotator {
    float Pitch;  // X轴旋转 (俯仰)
    float Yaw;    // Z轴旋转 (偏航)  
    float Roll;   // Y轴旋转 (翻滚)
};

struct MinimalViewInfo {
    VecTor3 Location;  // 相机位置
    FRotator Rotation; // 相机旋转
    float FOV;         // 视野角度
};
```

### 2. 新增核心函数
- `FMatrix RotatorToMatrix(FRotator rotation)` - 将旋转器转换为矩阵
- `VecTor2 WorldToScreen(VecTor3 worldLocation, MinimalViewInfo viewInfo, int width, int height)` - 使用MinimalViewInfo的坐标转换

### 3. 新增WorldTurnScreen函数重载
为所有现有的WorldTurnScreen函数添加了使用MinimalViewInfo的重载版本：
- `bool WorldTurnScreen(float &Screen, VecTor3 World, MinimalViewInfo &viewInfo)`
- `bool WorldTurnScreen(VecTor2 &Screen, VecTor3 World, MinimalViewInfo &viewInfo)`
- `bool WorldTurnScreen(VecTor2 &Screen, VecTor3 World, bool sig, MinimalViewInfo &viewInfo)`
- `bool WorldTurnScreen(VecTor4 &Screen, VecTor3 World, MinimalViewInfo &viewInfo)`
- `bool WorldTurnScreen(VecTor4 &Screen, float &ScreenCamera, VecTor3 World, MinimalViewInfo &viewInfo)`
- `bool WorldTurnScreen(VecTor4 &Screen, float &ScreenCamera, VecTor3 World, bool sig, MinimalViewInfo &viewInfo)`

### 4. 新增GetBoneTransform函数重载
- `void GetBoneTransform(int Count, VecTor2 &WorldBone, MinimalViewInfo &viewInfo)`
- `void GetBoneTransform(int Count, VecTor3 &BoneCoordinates, VecTor2 &WorldBone, MinimalViewInfo &viewInfo)`

### 5. 新增ExplosionRange函数重载
- `void ExplosionRange(VecTor3 Obj, ImColor color, float Range, float thickn, MinimalViewInfo &viewInfo)`
- `void ExplosionRangeFilled(VecTor3 Obj, ImColor color, float Range, int num_segments, MinimalViewInfo &viewInfo)`

### 6. 修改ImGuiTOOL类 (ImGuiTOOL.h)
添加了MinimalViewInfo成员变量：
```cpp
MinimalViewInfo viewInfo;
```

### 7. 修改ImGuiDraw.cpp
#### 相机数据读取部分：
```cpp
// 原来的代码
uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
if (read(MatrixAddress, &Matrix[0][0], 64)) {
    touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
}

// 新的代码
uintptr_t playerCameraManager = GetPointer(ModulesBase[0], 0x12132D60, 0x20);
if (playerCameraManager != 0) {
    read(playerCameraManager + 0x1130 + 0x10, &viewInfo.Location, sizeof(viewInfo.Location));
    read(playerCameraManager + 0x1130 + 0x10 + 0x0C, &viewInfo.Rotation, sizeof(viewInfo.Rotation));
    read(playerCameraManager + 0x1130 + 0x10 + 0x30, &viewInfo.FOV, sizeof(viewInfo.FOV));
    touch_information.Scal = viewInfo.FOV / 90.0f;
}
```

#### 函数调用更新：
所有的WorldTurnScreen、GetBoneTransform调用都添加了viewInfo参数。

## 使用方式

### 基本坐标转换
```cpp
VecTor3 worldPos = {1000, 2000, 100};
VecTor2 screenPos = WorldToScreen(worldPos, viewInfo, screenWidth, screenHeight);
```

### 带边界检查的转换
```cpp
VecTor4 screenCoords;
float screenCamera;
if (WorldTurnScreen(screenCoords, screenCamera, worldPos, viewInfo)) {
    // 在屏幕范围内，可以绘制
}
```

### 骨骼转换
```cpp
VecTor3 boneWorldPos;
VecTor2 boneScreenPos;
GetBoneTransform(5, boneWorldPos, boneScreenPos, viewInfo);
```

## 优势

1. **更直观**: 直接使用相机位置、旋转和FOV，比矩阵更容易理解
2. **更灵活**: 可以轻松修改FOV、相机位置等参数
3. **更易调试**: 可以直接查看和修改相机参数
4. **更符合现代3D编程习惯**: 大多数3D引擎都使用类似的相机表示方式
5. **向后兼容**: 原有的Matrix方式仍然可用

## 注意事项

1. 确保playerCameraManager的偏移地址正确
2. 旋转角度使用度数制，函数内部会转换为弧度
3. FOV影响透视投影的计算
4. 新旧函数可以并存，可以逐步迁移

## 文件清单

- `jni/src/ImGuiELGS/ImGuiELGS.h` - 添加了新结构体和函数
- `jni/src/ImGuiTOOL/ImGuiTOOL.h` - 添加了MinimalViewInfo成员
- `jni/src/ImGuiDraw.cpp` - 修改了相机数据读取和函数调用
- `jni/example_minimalviewinfo_usage.cpp` - 使用示例
- `MinimalViewInfo_Migration_Summary.md` - 本文档
