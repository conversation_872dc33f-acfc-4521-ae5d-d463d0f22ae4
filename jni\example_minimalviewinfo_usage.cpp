/*
 * 示例：如何使用MinimalViewInfo替代Matrix进行世界坐标转屏幕坐标
 * 
 * 这个文件展示了如何从原来的Matrix方式迁移到MinimalViewInfo方式
 */

#include "ImGuiELGS/ImGuiELGS.h"

// 原来的方式 (使用Matrix)
void OldWay_ReadCameraData() {
    // 原来的代码
    uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
    if (read(MatrixAddress, &Matrix[0][0], 64)) {
        touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
    }
    
    // 使用Matrix进行坐标转换
    VecTor3 worldPos = {100, 200, 300};
    VecTor2 screenPos;
    WorldTurnScreen(screenPos, worldPos);
}

// 新的方式 (使用MinimalViewInfo)
void NewWay_ReadCameraData() {
    // 新的代码 - 读取相机信息
    uintptr_t playerCameraManager = GetPointer(ModulesBase[0], 0x12132D60, 0x20); // 根据你的偏移调整
    
    if (playerCameraManager != 0) {
        // 读取相机位置、旋转和FOV
        read(playerCameraManager + 0x1130 + 0x10, &viewInfo.Location, sizeof(viewInfo.Location));
        read(playerCameraManager + 0x1130 + 0x10 + 0x0C, &viewInfo.Rotation, sizeof(viewInfo.Rotation));
        read(playerCameraManager + 0x1130 + 0x10 + 0x30, &viewInfo.FOV, sizeof(viewInfo.FOV));
        
        // 计算缩放因子
        touch_information.Scal = viewInfo.FOV / 90.0f;
    }
    
    // 使用MinimalViewInfo进行坐标转换
    VecTor3 worldPos = {100, 200, 300};
    VecTor2 screenPos;
    WorldTurnScreen(screenPos, worldPos, viewInfo);
}

// 示例：如何在绘制函数中使用新的方式
void ExampleDrawFunction() {
    // 假设我们有一个敌人的世界坐标
    VecTor3 enemyWorldPos = {1000, 2000, 100};
    
    // 使用新的MinimalViewInfo方式转换坐标
    VecTor4 enemyScreenCoords;
    float enemyScreenCamera;
    
    if (WorldTurnScreen(enemyScreenCoords, enemyScreenCamera, enemyWorldPos, viewInfo)) {
        // 敌人在屏幕范围内，可以绘制
        ImDrawList* drawList = ImGui::GetForegroundDrawList();
        
        // 绘制敌人方框
        float boxWidth = enemyScreenCoords.z;
        float boxHeight = enemyScreenCoords.w;
        
        drawList->AddRect(
            {enemyScreenCoords.x - boxWidth/2, enemyScreenCoords.y - boxHeight},
            {enemyScreenCoords.x + boxWidth/2, enemyScreenCoords.y},
            ImColor(255, 0, 0, 255),
            0.0f,
            0,
            2.0f
        );
        
        // 绘制距离信息
        char distanceText[64];
        sprintf(distanceText, "%.1fm", enemyScreenCamera);
        drawList->AddText(
            {enemyScreenCoords.x, enemyScreenCoords.y - boxHeight - 20},
            ImColor(255, 255, 255, 255),
            distanceText
        );
    }
}

// 示例：骨骼绘制使用新方式
void ExampleBoneDrawing() {
    // 假设我们有骨骼数据
    VecTor3 headBone = {1000, 2000, 150};
    VecTor3 neckBone = {1000, 2000, 140};
    
    VecTor2 headScreen, neckScreen;
    
    // 使用新的MinimalViewInfo方式转换骨骼坐标
    if (WorldTurnScreen(headScreen, headBone, viewInfo) && 
        WorldTurnScreen(neckScreen, neckBone, viewInfo)) {
        
        ImDrawList* drawList = ImGui::GetForegroundDrawList();
        
        // 绘制骨骼连线
        drawList->AddLine(
            {headScreen.x, headScreen.y},
            {neckScreen.x, neckScreen.y},
            ImColor(0, 255, 0, 255),
            2.0f
        );
    }
}

// 示例：爆炸范围绘制使用新方式
void ExampleExplosionRange() {
    VecTor3 explosionCenter = {1500, 1500, 50};
    float explosionRadius = 100.0f;
    
    // 使用新的MinimalViewInfo方式绘制爆炸范围
    ExplosionRange(explosionCenter, ImColor(255, 255, 0, 128), explosionRadius, 2.0f, viewInfo);
    ExplosionRangeFilled(explosionCenter, ImColor(255, 0, 0, 64), explosionRadius, 32, viewInfo);
}

/*
 * 迁移指南：
 * 
 * 1. 将 MatrixAddress 读取改为 playerCameraManager 读取
 * 2. 将 read(MatrixAddress, &Matrix[0][0], 64) 改为读取 MinimalViewInfo 数据
 * 3. 将所有 WorldTurnScreen 调用添加 viewInfo 参数
 * 4. 将所有 GetBoneTransform 调用添加 viewInfo 参数
 * 5. 将所有 ExplosionRange 调用添加 viewInfo 参数
 * 
 * 优势：
 * - 更直观的相机数据表示
 * - 更容易理解和调试
 * - 更符合现代3D图形编程习惯
 * - 可以更容易地实现高级功能如自定义FOV等
 */
