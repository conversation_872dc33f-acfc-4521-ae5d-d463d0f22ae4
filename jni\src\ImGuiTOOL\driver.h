#ifndef DRIVER_H
#define DRIVER_H
#include <unistd.h>

// 驱动接口类，支持内核版本4.9~6.6
class Driver
{
  private:
    
    // 类ID，记录类起始地址，防止越界异常
	uint64_t gid;
	// 类ID,记录类结束地址,防止越界异常
	uint64_t uid;

  public:
	Driver()
	{
		initkey("aa0ebe172d8655e373e0250cd6e69c883e7681a055a80de4e72ddfce0843802747420940");
	}

	// 版本ID(密钥)，防止版本接口不一致导致内核崩溃，切勿随意更换
	void initkey(char *key);
	
	// CPU亲和设置，指定运行在哪颗CPU上
	void cpuset(int num);
	
	// CPU亲和设置，指定范围随机运行在哪个CPU上，默认CPU0-CPU4
	void cpuset(int start, int end);
    
    // 初始化pid，创建一个对象务必要初始一次，返回true初始化成功，返回false初始化失败
	bool initpid(pid_t pid);
	
	// 获取进程pid，传入进程(包名)，从内核层安全获取pid
	pid_t get_pid(char *name);
	
	// 重载方法，获取进程pid，传入进程(包名)，comm字段，适用于获取线程pid、以及部分混淆包名的应用，从内核层安全获取pid
	pid_t get_pid(char *name, char *comm);
	
	// 获取模块地址，传入pid、模块名，从内核层安全获取模块地址，支持多线程
	uintptr_t get_module_base(pid_t pid, char *name);
	
	// 获取模块地址，传入pid、模块名、模块(长度)大小，从内核层安全获取模块地址，支持多线程
	uintptr_t get_module_base(pid_t pid, char *name, size_t size);
	
	// 硬件级读取数据，直接读硬件地址，传入地址、接收指针、类型大小，指数级安全，由于不使用CPU缓存，效率相应降低，支持多线程，支持QGKI、GKI2.0+
	bool read_safe(uintptr_t addr, void *buffer, size_t size);
	    
    // 内核层读取数据，只读已映射到内核空间的地址，传入地址、接收指针、类型大小，读前记录CPU缓存状态，读完后恢复CPU缓存行状态，支持多线程，效率较高
	bool read(uintptr_t addr, void *buffer, size_t size);
	
	// 内核层修改数据，只写已映射到内核空间的地址，传入地址、数据指针、类型大小，支持多线程
	bool write(uintptr_t addr, void *buffer, size_t size);

	// 模板方法，传入地址，返回地址上的值，支持多线程
	template <typename T> T read_safe(uintptr_t addr)
	{
		T res{};
		if (this->read_safe(addr, &res, sizeof(T)))
			return res;
		return 0;
	}
		
	// 模板方法，传入地址，返回地址上的(数据)值，支持多线程
	template <typename T> T read(uintptr_t addr)
	{
		T res{};
		if (this->read(addr, &res, sizeof(T)))
			return res;
		return 0;
	}
	
	// 模板方法，传入地址，修改后的(数据)值，支持多线程
	template <typename T> bool write(uintptr_t addr, T value)
	{
		return this->write(addr, &value, sizeof(T));
	}
	
};


#endif
